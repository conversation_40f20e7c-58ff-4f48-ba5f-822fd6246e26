// image_pinch_zooming.dart

import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A highly optimized widget for pinch-to-zoom interaction on images.
class ImagePinchZooming extends StatefulWidget {
  const ImagePinchZooming({
    super.key,
    required this.image,
    this.zoomedBackgroundColor = Colors.black54,
    this.hideStatusBarWhileZooming = false,
    this.minScale = 1.0,
    this.maxScale = 4.0,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onTwoFingersOn,
    this.onTwoFingersOff,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final Widget image;
  final Color zoomedBackgroundColor;
  final bool hideStatusBarWhileZooming;
  final double minScale;
  final double maxScale;
  final Duration animationDuration;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onTwoFingersOn;
  final VoidCallback? onTwoFingersOff;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  @override
  State<ImagePinchZooming> createState() => _ImagePinchZoomingState();
}

class _ImagePinchZoomingState extends State<ImagePinchZooming>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  OverlayEntry? _overlayEntry;
  Offset? _initialFocalPoint;
  Offset? _widgetOrigin;
  Size? _widgetSize;
  bool _isZooming = false;
  bool _isReversing = false;
  int _activePointers = 0;
  Timer? _updateThrottleTimer;

  // Store original system UI mode for proper restoration
  SystemUiMode? _originalSystemUiMode;

  // Use a single key for overlay management
  final GlobalKey<_PinchZoomOverlayState> _overlayKey = GlobalKey();

  // Track app lifecycle for system UI restoration
  bool _isAppInForeground = true;

  @override
  void initState() {
    super.initState();

    // Add app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Store original system UI mode if we need to hide status bar
    if (widget.hideStatusBarWhileZooming) {
      _storeOriginalSystemUiMode();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _updateThrottleTimer?.cancel();
    _cleanupOverlay();
    _restoreSystemUI();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isAppInForeground = false;
        // Restore system UI when app goes to background
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _restoreSystemUI();
        }
        break;
      case AppLifecycleState.resumed:
        _isAppInForeground = true;
        // Re-hide system UI when app comes back to foreground
        if (_isZooming && widget.hideStatusBarWhileZooming) {
          _handleSystemUI();
        }
        break;
      case AppLifecycleState.detached:
        _isAppInForeground = false;
        break;
      case AppLifecycleState.hidden:
        _isAppInForeground = false;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: RawGestureDetector(
        gestures: {
          _CustomScaleGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<
                _CustomScaleGestureRecognizer
              >(() => _CustomScaleGestureRecognizer(), (
                _CustomScaleGestureRecognizer instance,
              ) {
                instance.onStart = _handleScaleStart;
                instance.onUpdate = _handleScaleUpdate;
                instance.onEnd = _handleScaleEnd;
              }),
          _CustomTapGestureRecognizer:
              GestureRecognizerFactoryWithHandlers<_CustomTapGestureRecognizer>(
                () => _CustomTapGestureRecognizer(),
                (_CustomTapGestureRecognizer instance) {
                  instance.onTap = widget.onTap;
                  instance.onDoubleTap = widget.onDoubleTap;
                  instance.onLongPress = widget.onLongPress;
                },
              ),
        },
        child: Listener(
          onPointerDown: _handlePointerDown,
          onPointerUp: _handlePointerUp,
          onPointerCancel: _handlePointerCancel,
          child: Visibility(
            visible: !_isZooming,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: widget.image,
          ),
        ),
      ),
    );
  }

  void _handlePointerDown(PointerDownEvent event) {
    _activePointers++;

    if (_activePointers >= 2) {
      widget.onTwoFingersOn?.call();
    }
  }

  void _handlePointerUp(PointerUpEvent event) {
    _activePointers = (_activePointers - 1).clamp(0, 10);

    if (_activePointers < 2) {
      widget.onTwoFingersOff?.call();
    }
  }

  void _handlePointerCancel(PointerCancelEvent event) {
    _activePointers = 0;
    widget.onTwoFingersOff?.call();
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (_overlayEntry != null || _isReversing || _activePointers < 2) return;

    try {
      // Safely get widget bounds with null safety
      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox?.hasSize != true || !renderBox!.attached) return;

      _widgetSize = renderBox.size;
      _widgetOrigin = renderBox.localToGlobal(Offset.zero);
      _initialFocalPoint = details.focalPoint;

      if (mounted) {
        setState(() {
          _isZooming = true;
        });
      }

      _handleSystemUI();
      widget.onZoomStart?.call();
      _showOverlay();
    } catch (e) {
      // Handle any coordinate calculation errors gracefully
      debugPrint('Error in _handleScaleStart: $e');
      return;
    }
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_isReversing || _activePointers < 2) return;

    // Throttle updates to improve performance
    _updateThrottleTimer?.cancel();
    _updateThrottleTimer = Timer(const Duration(milliseconds: 16), () {
      _performScaleUpdate(details);
    });
  }

  void _performScaleUpdate(ScaleUpdateDetails details) {
    // Check if overlay and its state are available
    final overlayState = _overlayKey.currentState;
    if (overlayState == null ||
        _widgetOrigin == null ||
        _initialFocalPoint == null) {
      return;
    }

    try {
      final clampedScale = details.scale.clamp(
        widget.minScale,
        widget.maxScale,
      );
      final newPosition =
          _widgetOrigin! + (details.focalPoint - _initialFocalPoint!);

      overlayState._updateTransform(newPosition, clampedScale);
    } catch (e) {
      debugPrint('Error in _performScaleUpdate: $e');
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) async {
    if (_isReversing || !_isZooming) return;

    _isReversing = true;
    _updateThrottleTimer?.cancel();
    widget.onZoomEnd?.call();

    try {
      await _overlayKey.currentState?._animateToOriginal();
    } catch (e) {
      debugPrint('Error in scale end animation: $e');
    }

    _cleanupOverlay();
    _restoreSystemUI();

    if (mounted) {
      setState(() {
        _isZooming = false;
        _isReversing = false;
      });
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null || _widgetSize == null || _widgetOrigin == null) {
      return;
    }

    try {
      final overlay = Overlay.of(context);
      _overlayEntry = OverlayEntry(
        builder: (context) => _PinchZoomOverlay(
          key: _overlayKey,
          image: widget.image,
          originalSize: _widgetSize!,
          originalPosition: _widgetOrigin!,
          backgroundColor: widget.zoomedBackgroundColor,
          animationDuration: widget.animationDuration,
        ),
      );

      overlay.insert(_overlayEntry!);
    } catch (e) {
      debugPrint('Error showing overlay: $e');
      _overlayEntry = null;
    }
  }

  void _cleanupOverlay() {
    try {
      _overlayEntry?.remove();
    } catch (e) {
      debugPrint('Error removing overlay: $e');
    }

    _overlayEntry = null;
    _widgetOrigin = null;
    _widgetSize = null;
    _initialFocalPoint = null;
  }

  void _storeOriginalSystemUiMode() {
    // Note: There's no direct way to query current SystemUiMode in Flutter
    // We'll assume the most common default mode
    _originalSystemUiMode = SystemUiMode.edgeToEdge;
  }

  void _handleSystemUI() {
    if (!widget.hideStatusBarWhileZooming || !_isAppInForeground) return;

    try {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } catch (e) {
      debugPrint('Error setting system UI mode: $e');
    }
  }

  void _restoreSystemUI() {
    if (!widget.hideStatusBarWhileZooming) return;

    try {
      // Restore to original mode if stored, otherwise use sensible default
      SystemChrome.setEnabledSystemUIMode(
        _originalSystemUiMode ?? SystemUiMode.edgeToEdge,
      );
    } catch (e) {
      debugPrint('Error restoring system UI mode: $e');
    }
  }
}

// Custom gesture recognizers to handle conflicts better
class _CustomScaleGestureRecognizer extends ScaleGestureRecognizer {
  @override
  void rejectGesture(int pointer) {
    // Accept gesture even if parent tries to claim it
    acceptGesture(pointer);
  }
}

class _CustomTapGestureRecognizer extends TapGestureRecognizer {
  VoidCallback? onDoubleTap;
  VoidCallback? onLongPress;

  @override
  void rejectGesture(int pointer) {
    // Accept gesture even if parent tries to claim it for single taps
    acceptGesture(pointer);
  }
}

// Overlay widget for zoom effect
class _PinchZoomOverlay extends StatefulWidget {
  final Widget image;
  final Size originalSize;
  final Offset originalPosition;
  final Color backgroundColor;
  final Duration animationDuration;

  const _PinchZoomOverlay({
    super.key,
    required this.image,
    required this.originalSize,
    required this.originalPosition,
    required this.backgroundColor,
    required this.animationDuration,
  });

  @override
  State<_PinchZoomOverlay> createState() => _PinchZoomOverlayState();
}

class _PinchZoomOverlayState extends State<_PinchZoomOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _opacityAnimation;

  Offset _currentPosition = Offset.zero;
  double _currentScale = 1.0;
  bool _isAnimating = false;

  // Cache screen size for performance
  Size? _cachedScreenSize;

  @override
  void initState() {
    super.initState();
    _currentPosition = widget.originalPosition;
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    if (_animationController.isAnimating) {
      _animationController.stop();
    }
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Cache screen size on first build
    _cachedScreenSize ??= MediaQuery.of(context).size;

    if (_isAnimating) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return _buildOverlayContent(
            _positionAnimation.value,
            _scaleAnimation.value,
            _opacityAnimation.value,
          );
        },
      );
    }

    return _buildOverlayContent(_currentPosition, _currentScale, _getOpacity());
  }

  Widget _buildOverlayContent(Offset position, double scale, double opacity) {
    return RepaintBoundary(
      child: Stack(
        children: [
          // Background with opacity animation
          if (opacity > 0.0)
            AnimatedOpacity(
              opacity: opacity,
              duration: const Duration(milliseconds: 100),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: widget.backgroundColor,
              ),
            ),
          // Zoomed image with transform
          RepaintBoundary(
            child: Transform.translate(
              offset: position,
              child: Transform.scale(
                scale: scale,
                child: SizedBox(
                  width: widget.originalSize.width,
                  height: widget.originalSize.height,
                  child: widget.image,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getOpacity() {
    // Safe opacity calculation with cached screen size
    if (_currentScale <= 1.0) return 0.0;
    if (_cachedScreenSize == null) return 1.0;

    final screenHeight = _cachedScreenSize!.height;
    final maxScale = screenHeight / widget.originalSize.height;

    if (maxScale <= 1.0) return 1.0;

    return ((_currentScale - 1.0) / (maxScale - 1.0)).clamp(0.0, 1.0);
  }

  void _updateTransform(Offset position, double scale) {
    if (_isAnimating || !mounted) return;

    // Batch updates to reduce rebuilds
    if (_currentPosition != position || _currentScale != scale) {
      setState(() {
        _currentPosition = position;
        _currentScale = scale;
      });
    }
  }

  Future<void> _animateToOriginal() async {
    if (_isAnimating || !mounted) return;

    _isAnimating = true;

    try {
      _scaleAnimation = Tween<double>(begin: _currentScale, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.fastOutSlowIn,
        ),
      );

      _positionAnimation =
          Tween<Offset>(
            begin: _currentPosition,
            end: widget.originalPosition,
          ).animate(
            CurvedAnimation(
              parent: _animationController,
              curve: Curves.fastOutSlowIn,
            ),
          );

      _opacityAnimation = Tween<double>(begin: _getOpacity(), end: 0.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Curves.fastOutSlowIn,
        ),
      );

      await _animationController.forward(from: 0.0);

      if (mounted) {
        _animationController.reset();
      }
    } catch (e) {
      debugPrint('Error in animation: $e');
    } finally {
      _isAnimating = false;
    }
  }
}
